class AI_Helper:
    def __init__(self,name,age,height,mood,age_breakfst,weather):
        self.name = name
        self.age = age
        self.height = height
        self.mood = mood
        self.ate_breakfast = ate_breakfast
        self.weather = weather

    def show_info(self):
        print("名字：", self.name)
        print("年齡：", self.age)
        print("身高：", self.height)
        print("心情：", self.mood)
        print("今天吃早餐：", self.ate_breakfast)
        print("天氣：", self.weather)

    def check_breakfast(self):
        if self.ate_breakfast:
            print("今天已經吃早餐囉！")
        else:
            print("記得吃早餐喔！")

    def check_age(self):
        if self.age >=18:
            print("成年人")
        else:
            print("未成年人")

    def check_mood(self):
        if self.mood == "開心":
            print("保持好心情！")
        else:
            print("加油，今天會更好！")

    def check_weather(self):
        if self.weather == "下雨":
            print("記得帶傘喔！")
        else:
            print("天氣不錯，出去走走吧！")

#使用者輸入
name = input("請輸入你的名字：")
age = int(input("請輸入你的年齡："))
height = float(input("請輸入你的身高(cm)："))
mood = input("今天心情如何？")
ate_breakfast_input = input("今天有吃早餐嗎？(是/否):")
weather = input("今天天氣如何？(下雨/晴天):")
ate_breakfast = True if ate_breakfast_input == "是" else False

ai = AI_Helper(name,age,height,mood ,ate_breakfast,weather)

ai.show_info()
ai.check_breakfast()
ai.check_age()
ai.check_mood()
ai.check_weather()

