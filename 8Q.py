class EightQueens:
    def __init__(self):
        self.board_size = 8
        self.solutions = []

    def is_safe(self, board, row, col):
        """
        Check if placing a queen at (row, col) is safe
        """
        # Check column
        for i in range(row):
            if board[i] == col:
                return False

        # Check diagonal (top-left to bottom-right)
        for i in range(row):
            if abs(board[i] - col) == abs(i - row):
                return False

        return True

    def solve_nqueens_backtrack(self, board, row):
        """
        Solve N-Queens using backtracking
        board: list where board[i] represents the column of queen in row i
        """
        if row == self.board_size:
            # Found a solution
            self.solutions.append(board[:])
            return True

        for col in range(self.board_size):
            if self.is_safe(board, row, col):
                board[row] = col
                if self.solve_nqueens_backtrack(board, row + 1):
                    return True
                # Backtrack
                board[row] = -1

        return False

    def solve_all_solutions(self):
        """
        Find all solutions to the 8-Queens problem
        """
        self.solutions = []
        board = [-1] * self.board_size
        self._solve_all_recursive(board, 0)
        return self.solutions

    def _solve_all_recursive(self, board, row):
        """
        Helper method to find all solutions recursively
        """
        if row == self.board_size:
            self.solutions.append(board[:])
            return

        for col in range(self.board_size):
            if self.is_safe(board, row, col):
                board[row] = col
                self._solve_all_recursive(board, row + 1)
                board[row] = -1

    def print_board(self, solution):
        """
        Print a visual representation of the board
        """
        print("\n" + "="*25)
        for row in range(self.board_size):
            line = ""
            for col in range(self.board_size):
                if solution[row] == col:
                    line += "Q "
                else:
                    line += ". "
            print(f"{row + 1} {line}")
        print("  " + " ".join([str(i+1) for i in range(self.board_size)]))
        print("="*25)

    def print_solution_compact(self, solution):
        """
        Print solution in compact format
        """
        print(f"Solution: {[pos + 1 for pos in solution]}")

    def solve_first_solution(self):
        """
        Find and return the first solution
        """
        board = [-1] * self.board_size
        if self.solve_nqueens_backtrack(board, 0):
            return board
        return None


def main():
    print("8 Queens Problem Solver")
    print("=" * 30)

    queens = EightQueens()

    # Find first solution
    print("\n1. Finding first solution...")
    first_solution = queens.solve_first_solution()
    if first_solution:
        print("First solution found:")
        queens.print_solution_compact(first_solution)
        queens.print_board(first_solution)
    else:
        print("No solution found!")

    # Find all solutions
    print("\n2. Finding all solutions...")
    all_solutions = queens.solve_all_solutions()
    print(f"Total number of solutions: {len(all_solutions)}")

    # Display first few solutions
    print("\nFirst 3 solutions:")
    for i, solution in enumerate(all_solutions[:3]):
        print(f"\nSolution {i + 1}:")
        queens.print_solution_compact(solution)
        queens.print_board(solution)

    # Interactive mode
    print(f"\nWould you like to see more solutions? (Total: {len(all_solutions)})")
    while True:
        try:
            choice = input("\nEnter solution number (1-92) or 'q' to quit: ").strip()
            if choice.lower() == 'q':
                break

            solution_num = int(choice)
            if 1 <= solution_num <= len(all_solutions):
                print(f"\nSolution {solution_num}:")
                queens.print_solution_compact(all_solutions[solution_num - 1])
                queens.print_board(all_solutions[solution_num - 1])
            else:
                print(f"Please enter a number between 1 and {len(all_solutions)}")
        except ValueError:
            print("Please enter a valid number or 'q' to quit")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break


if __name__ == "__main__":
    main()